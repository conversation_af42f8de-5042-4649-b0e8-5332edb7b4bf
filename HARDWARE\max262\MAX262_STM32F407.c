//-----------------------------------------------------------------
// 文件描述: 
//		MAX262滤波器驱动 - 适配STM32F407ZG
// 作    者: 正点原子 (原版) / 修改适配STM32F407ZG
// 开始日期: 2018-05-24
// 完成日期: 2018-05-24
// 修改记录: 2025-01-XX - 适配STM32F407ZG，使用未占用引脚
// 版    本: V2.0
//   - V1.0: 原始模块
//   - V2.0: 适配STM32F407ZG，使用标准库函数
// 测试硬件: STM32F407ZG开发板
// 说    明: 使用未占用的GPIOB和GPIOC引脚
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include "stm32f4xx.h"
#include "MAX262_STM32F407.h"
#include "delay.h"
#include <math.h>

extern int Fn;

//-----------------------------------------------------------------
// GPIO初始化函数
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOB和GPIOC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB | RCC_AHB1Periph_GPIOC, ENABLE);
    
    // 配置GPIOB引脚 (D0, D1)
    GPIO_InitStructure.GPIO_Pin = D0_GPIO_PIN | D1_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(D0_GPIO_PORT, &GPIO_InitStructure);
    
    // 配置GPIOC引脚 (A0-A3, LE, WR)
    GPIO_InitStructure.GPIO_Pin = A0_GPIO_PIN | A1_GPIO_PIN | A2_GPIO_PIN | 
                                  A3_GPIO_PIN | LE_GPIO_PIN | WR_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(A0_GPIO_PORT, &GPIO_InitStructure);
    
    // 初始化引脚状态
    D0_L;
    D1_L;
    A0_L;
    A1_L;
    A2_L;
    A3_L;
    LE_L;
    WR_H;
}

//-----------------------------------------------------------------
// uint8_t Qn(float q)                     
//-----------------------------------------------------------------
// 函数功能: 计算Q值对应的控制字N
// 输入参数: Q
// 返 回 值: 控制字N
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
uint8_t Qn(float q)
{
    uint8_t temp;
    temp = 128 - (64/q);             // 根据芯片手册相关公式
    return temp; 
}

//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器1的模式、频率以及Q值配置
// 输入参数: 模式 mode, 阻止/通过 频率 f, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter1(uint8_t mode, float q)
{
    uint8_t i;
    uint8_t a = 0x03;
    uint8_t sq;
    i = sq = 0;
    
    sq = Qn(q);                     // 求Q对应的控制字N
    LE_H;                           // 使能锁存器
    delay_us(1);                    // 使用微秒延时
    WR_H;                           // 写端口置高
    delay_us(1);
    
    // 根据芯片手册 P15 Table4	
    
    // 清除所有数据和地址位
    D0_L; D1_L;
    A0_L; A1_L; A2_L; A3_L;
    delay_us(1);
    WR_L;                           // 写端口置低
    delay_us(1);
    
    // 写入模式数据到D1, D0
    if(mode & 0x01) D0_H; else D0_L;
    if(mode & 0x02) D1_H; else D1_L;
    delay_us(1);
    WR_H;                           // 写端口置高
    delay_us(1);
    
    // 写入频率控制字 (3个字节)
    for(i = 0; i < 3; i++)
    {
        // 清除所有位
        D0_L; D1_L;
        A0_L; A1_L; A2_L; A3_L;
        
        // 设置地址
        if((i+1) & 0x01) A0_H;
        if((i+1) & 0x02) A1_H;
        if((i+1) & 0x04) A2_H;
        if((i+1) & 0x08) A3_H;
        
        delay_us(1);
        WR_L;                             // 写使能
        delay_us(1);
        
        // 写入频率数据
        if(Fn & (a << (2*i))) D0_H; else D0_L;
        if(Fn & (a << (2*i + 1))) D1_H; else D1_L;
        
        delay_us(1);
        WR_H;                             // 写禁止
        delay_us(1);
    }
    
    a = 0x03;
    
    // 写入Q值控制字 (4个字节)
    for(i = 0; i < 4; i++)
    {
        // 清除所有位
        D0_L; D1_L;
        A0_L; A1_L; A2_L; A3_L;
        
        // 设置地址
        if((i+4) & 0x01) A0_H;
        if((i+4) & 0x02) A1_H;
        if((i+4) & 0x04) A2_H;
        if((i+4) & 0x08) A3_H;
        
        delay_us(1);
        WR_L;                             // 写使能
        delay_us(1);
        
        // 写入Q值数据
        if(sq & (a << (2*i))) D0_H; else D0_L;
        if(sq & (a << (2*i + 1))) D1_H; else D1_L;
        
        delay_us(1);                   
        WR_H;                             // 写禁止
        delay_us(1);
    }
}

//-----------------------------------------------------------------
// void Filter2(uint8_t mode, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器2的模式、频率以及Q值配置
// 输入参数: 模式 mode, 阻止/通过 频率 f, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter2(uint8_t mode, float q)
{
    uint8_t i;
    uint8_t a = 0x03;
    uint8_t sq;
    i = sq = 0;
    
    sq = Qn(q);                     // 求Q对应的控制字N
    LE_H;                           // 使能锁存器
    delay_us(1);
    WR_H;                           // 写端口置高
    delay_us(1);
    
    // 清除所有数据和地址位
    D0_L; D1_L;
    A0_L; A1_L; A2_L; A3_L;
    
    // 设置地址为8 (滤波器2的模式地址)
    A3_H;  // 8 = 1000b
    
    delay_us(1);
    WR_L;                           // 写端口置低
    delay_us(1);
    
    // 写入模式数据到D1, D0
    if(mode & 0x01) D0_H; else D0_L;
    if(mode & 0x02) D1_H; else D1_L;
    delay_us(1);
    WR_H;                           // 写端口置高
    delay_us(1);
    
    // 写入频率控制字 (3个字节，地址9-11)
    for(i = 0; i < 3; i++)
    {
        // 清除所有位
        D0_L; D1_L;
        A0_L; A1_L; A2_L; A3_L;
        
        // 设置地址 (i+9)
        if((i+9) & 0x01) A0_H;
        if((i+9) & 0x02) A1_H;
        if((i+9) & 0x04) A2_H;
        if((i+9) & 0x08) A3_H;
        
        delay_us(1);
        WR_L;                             // 写使能
        delay_us(1);
        
        // 写入频率数据
        if(Fn & (a << (2*i))) D0_H; else D0_L;
        if(Fn & (a << (2*i + 1))) D1_H; else D1_L;
        
        delay_us(1);                   
        WR_H;                             // 写禁止
        delay_us(1);
    }
    
    a = 0x03;
    
    // 写入Q值控制字 (4个字节，地址12-15)
    for(i = 0; i < 4; i++)
    {
        // 清除所有位
        D0_L; D1_L;
        A0_L; A1_L; A2_L; A3_L;
        
        // 设置地址 (i+12)
        if((i+12) & 0x01) A0_H;
        if((i+12) & 0x02) A1_H;
        if((i+12) & 0x04) A2_H;
        if((i+12) & 0x08) A3_H;
        
        delay_us(1);
        WR_L;                             // 写使能
        delay_us(1);
        
        // 写入Q值数据
        if(sq & (a << (2*i))) D0_H; else D0_L;
        if(sq & (a << (2*i + 1))) D1_H; else D1_L;
        
        delay_us(1);                   
        WR_H;                             // 写禁止
        delay_us(1);
    }
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
