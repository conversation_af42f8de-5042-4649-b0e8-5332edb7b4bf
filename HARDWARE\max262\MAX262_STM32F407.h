//-----------------------------------------------------------------
// MAX262驱动头文件 - 适配STM32F407ZG
// 头文件名: MAX262_STM32F407.h
// 作    者: 正点原子 (原版) / 修改适配STM32F407ZG
// 编写时间: 2014-01-28
// 修改记录: 2025-01-XX - 适配STM32F407ZG，使用未占用引脚
//-----------------------------------------------------------------

#ifndef _MAX262_STM32F407_H
#define _MAX262_STM32F407_H

#include "stm32f4xx.h"
#include "sys.h"

//-----------------------------------------------------------------
// 引脚定义 - 使用STM32F407ZG未占用的引脚
//-----------------------------------------------------------------
// 数据引脚 D0, D1 - 使用GPIOB未占用引脚
#define	D0_GPIO_PORT		GPIOB
#define	D0_GPIO_PIN			GPIO_Pin_0
#define D0_L				GPIO_ResetBits(D0_GPIO_PORT, D0_GPIO_PIN)
#define D0_H				GPIO_SetBits(D0_GPIO_PORT, D0_GPIO_PIN)

#define	D1_GPIO_PORT		GPIOB
#define	D1_GPIO_PIN			GPIO_Pin_1
#define	D1_L				GPIO_ResetBits(D1_GPIO_PORT, D1_GPIO_PIN)
#define	D1_H				GPIO_SetBits(D1_GPIO_PORT, D1_GPIO_PIN)

// 地址引脚 A0-A3 - 使用GPIOC未占用引脚
#define	A0_GPIO_PORT		GPIOC
#define	A0_GPIO_PIN			GPIO_Pin_0
#define	A0_L				GPIO_ResetBits(A0_GPIO_PORT, A0_GPIO_PIN)
#define	A0_H				GPIO_SetBits(A0_GPIO_PORT, A0_GPIO_PIN)
#define A0_IS_L				(GPIO_ReadInputDataBit(A0_GPIO_PORT, A0_GPIO_PIN) == Bit_RESET)

#define	A1_GPIO_PORT		GPIOC
#define	A1_GPIO_PIN			GPIO_Pin_2
#define	A1_L				GPIO_ResetBits(A1_GPIO_PORT, A1_GPIO_PIN)
#define	A1_H				GPIO_SetBits(A1_GPIO_PORT, A1_GPIO_PIN)
#define A1_IS_H				(GPIO_ReadInputDataBit(A1_GPIO_PORT, A1_GPIO_PIN) == Bit_SET)

#define	A2_GPIO_PORT		GPIOC
#define	A2_GPIO_PIN			GPIO_Pin_3
#define	A2_L				GPIO_ResetBits(A2_GPIO_PORT, A2_GPIO_PIN)
#define	A2_H				GPIO_SetBits(A2_GPIO_PORT, A2_GPIO_PIN)

#define	A3_GPIO_PORT		GPIOC
#define	A3_GPIO_PIN			GPIO_Pin_4
#define	A3_L				GPIO_ResetBits(A3_GPIO_PORT, A3_GPIO_PIN)
#define	A3_H				GPIO_SetBits(A3_GPIO_PORT, A3_GPIO_PIN)

// 控制引脚 LE, WR - 使用GPIOC未占用引脚
#define	LE_GPIO_PORT		GPIOC
#define	LE_GPIO_PIN			GPIO_Pin_5
#define	LE_L				GPIO_ResetBits(LE_GPIO_PORT, LE_GPIO_PIN)
#define	LE_H				GPIO_SetBits(LE_GPIO_PORT, LE_GPIO_PIN)

#define	WR_GPIO_PORT		GPIOC
#define	WR_GPIO_PIN			GPIO_Pin_6
#define	WR_L				GPIO_ResetBits(WR_GPIO_PORT, WR_GPIO_PIN)
#define	WR_H				GPIO_SetBits(WR_GPIO_PORT, WR_GPIO_PIN)

// 频率常数定义
#define NF1 139.8
#define NF2 40.48

// 时钟频率定义 (Hz)
#define MAX262_CLOCK_FREQ   139800.0f   // 139.8kHz

// 模式定义
#define MAX262_MODE_1       0x00        // 带通/低通 (Butterworth, Chebyshev, Bessel)
#define MAX262_MODE_2       0x01        // 全极点带通/低通 (高Q值)
#define MAX262_MODE_3       0x02        // 椭圆带通/低通/高通
#define MAX262_MODE_4       0x03        // 全通滤波器

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void);
void Filter1(uint8_t mode, float q);
void Filter2(uint8_t mode, float q);
uint8_t Qn(float q);

// 频率计算辅助函数
float MAX262_CalculateFrequency(uint8_t mode, int fn_value);
int MAX262_CalculateFn(uint8_t mode, float target_freq);
void MAX262_SetFrequency(uint8_t filter_num, uint8_t mode, float target_freq, float q);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
