//-----------------------------------------------------------------
// MAX262使用示例 - STM32F407ZG
// 文件名: MAX262_example.c
// 说明: 展示如何在STM32F407ZG项目中使用MAX262滤波器
//-----------------------------------------------------------------

#include "stm32f4xx.h"
#include "MAX262_STM32F407.h"
#include "delay.h"

// 全局变量 - 需要在main.c或其他地方定义
int Fn = 63;  // 频率控制字，对应139.8kHz时钟频率

//-----------------------------------------------------------------
// MAX262初始化函数
//-----------------------------------------------------------------
void MAX262_Init(void)
{
    // 初始化GPIO引脚
    MAX262_GPIO_Init();
    
    // 等待稳定
    delay_ms(10);
    
    // 可以在这里设置默认的滤波器参数
    // 例如：设置滤波器1为低通模式，Q值为1.0
    // Filter1(0x00, 1.0f);  // 0x00 = 低通模式
}

//-----------------------------------------------------------------
// 设置滤波器1参数
//-----------------------------------------------------------------
void MAX262_SetFilter1(uint8_t mode, float q_value)
{
    /*
    mode参数说明:
    0x00 = 低通滤波器
    0x01 = 高通滤波器  
    0x02 = 带通滤波器
    0x03 = 带阻滤波器
    
    q_value: Q值，范围通常为0.5到100
    */
    Filter1(mode, q_value);
}

//-----------------------------------------------------------------
// 设置滤波器2参数
//-----------------------------------------------------------------
void MAX262_SetFilter2(uint8_t mode, float q_value)
{
    /*
    mode参数说明:
    0x00 = 低通滤波器
    0x01 = 高通滤波器  
    0x02 = 带通滤波器
    0x03 = 带阻滤波器
    
    q_value: Q值，范围通常为0.5到100
    */
    Filter2(mode, q_value);
}

//-----------------------------------------------------------------
// 设置频率控制字
//-----------------------------------------------------------------
void MAX262_SetFrequency(int freq_control)
{
    /*
    freq_control: 频率控制字
    根据MAX262手册，频率计算公式为：
    f = (freq_control * fCLK) / (100 * 2^8)
    其中fCLK为时钟频率
    */
    Fn = freq_control;
}

//-----------------------------------------------------------------
// 使用示例函数
//-----------------------------------------------------------------
void MAX262_Example(void)
{
    // 初始化MAX262
    MAX262_Init();
    
    // 示例1: 设置滤波器1为低通滤波器，Q值为2.0
    MAX262_SetFilter1(0x00, 2.0f);
    delay_ms(100);
    
    // 示例2: 设置滤波器2为带通滤波器，Q值为5.0
    MAX262_SetFilter2(0x02, 5.0f);
    delay_ms(100);
    
    // 示例3: 改变频率控制字
    MAX262_SetFrequency(80);  // 设置新的频率控制字
    
    // 重新配置滤波器以应用新频率
    MAX262_SetFilter1(0x00, 2.0f);
    MAX262_SetFilter2(0x02, 5.0f);
}

//-----------------------------------------------------------------
// 在main.c中的使用方法示例
//-----------------------------------------------------------------
/*
在main.c中添加以下代码:

#include "MAX262_STM32F407.h"

int Fn = 63;  // 全局变量定义

int main(void)
{
    // 其他初始化代码...
    
    // 初始化MAX262
    MAX262_Init();
    
    // 配置滤波器
    MAX262_SetFilter1(0x00, 2.0f);  // 低通滤波器，Q=2.0
    MAX262_SetFilter2(0x02, 5.0f);  // 带通滤波器，Q=5.0
    
    while(1)
    {
        // 主循环代码...
        
        // 可以根据需要动态调整滤波器参数
        // MAX262_SetFilter1(0x01, 3.0f);  // 改为高通滤波器
        
        delay_ms(1000);
    }
}
*/

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
